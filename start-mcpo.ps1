# start-mcpo.ps1 - Start MCP Chrome (mcp-chrome) via mcpo and expose OpenAPI for Open WebUI
# Use start-mcpo.bat to run with ExecutionPolicy bypass.

param(
  [string]$ListenHost = "127.0.0.1",
  [int]$Port = 8000,
  [string]$ApiKey = ""  # Optional: if not empty, mcpo will require Bearer $ApiKey
)

# Do NOT make Write-Error terminating; we want the window to stay open on failure
$ErrorActionPreference = "Continue"

function Stop-And-Wait([string]$Message) {
  Write-Host $Message -ForegroundColor Red
  Read-Host -Prompt "Press Enter to exit"
  exit 1
}

# 1) Activate Python venv where mcpo is installed
$VenvDir = "C:\Users\<USER>\mcpo"
$ActivatePs1 = Join-Path $VenvDir "Scripts\Activate.ps1"
if (!(Test-Path $ActivatePs1)) {
  Stop-And-Wait "Cannot find Python venv: $VenvDir. Please create it and install mcpo there."
}
Write-Host "Activating venv: $VenvDir" -ForegroundColor Cyan
try { . $ActivatePs1 } catch { Stop-And-Wait "Failed to activate venv: $VenvDir" }

# 2) Resolve stdio server script from mcp-chrome-bridge
$StdioScript = "C:\Users\<USER>\AppData\Roaming\npm\node_modules\mcp-chrome-bridge\dist\mcp\mcp-server-stdio.js"
if (!(Test-Path $StdioScript)) {
  Stop-And-Wait "Cannot find stdio script: $StdioScript`nInstall bridge: npm install -g mcp-chrome-bridge && mcp-chrome-bridge register"
}

# 3) Quick checks
if (-not (Get-Command mcpo -ErrorAction SilentlyContinue)) {
  Stop-And-Wait "mcpo is not available in this venv. Run: pip install -i https://pypi.org/simple mcpo"
}
if (-not (Get-Command node -ErrorAction SilentlyContinue)) {
  Stop-And-Wait "Node.js is not on PATH. Install Node.js (https://nodejs.org) and reopen this script."
}

# 4) Build mcpo arguments
$mcpoArgs = @("--host", $ListenHost, "--port", $Port)
if ($ApiKey -and $ApiKey.Trim().Length -gt 0) {
  $mcpoArgs += @("--api-key", $ApiKey)
  Write-Host "mcpo will require Bearer token authentication" -ForegroundColor Yellow
}
$mcpoArgs += @("--", "node", $StdioScript)

Write-Host "Starting mcpo on http://$($ListenHost):$Port ..." -ForegroundColor Green
Write-Host "Press Ctrl+C to stop. Keep this window open while using Open WebUI tools."

# 5) Run mcpo in foreground to show logs
try {
  mcpo @mcpoArgs
} catch {
  Stop-And-Wait ("mcpo failed: " + $_)
}

Write-Host "mcpo terminated." -ForegroundColor Yellow
Read-Host -Prompt "Press Enter to exit"

